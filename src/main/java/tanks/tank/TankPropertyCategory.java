package tanks.tank;

public class TankPropertyCategory
{
    public static final String appearanceGeneral = "appearanceGeneral";
    public static final String appearanceEmblem = "appearanceEmblem";
    public static final String appearanceTurretBase = "appearanceTurretBase";
    public static final String appearanceTurretBarrel = "appearanceTurretBarrel";
    public static final String appearanceBody = "appearanceBody";
    public static final String appearanceTreads = "appearanceTreads";
    public static final String appearanceGlow = "appearanceGlow";
    public static final String appearanceTracks = "appearanceTracks";
    public static final String general = "general";
    public static final String movementGeneral = "movementGeneral";
    public static final String movementIdle = "movementIdle";
    public static final String movementAvoid = "movementAvoid";
    public static final String movementPathfinding = "movementPathfinding";
    public static final String movementOnSight = "movementOnSight";
    public static final String mines = "mines";
    public static final String firingGeneral = "firingGeneral";
    public static final String firingBehavior = "firingBehavior";
    public static final String firingPattern = "firingPattern";
    public static final String spawning = "spawning";
    public static final String transformationGeneral = "transformationGeneral";
    public static final String transformationOnSight = "transformationOnSight";
    public static final String transformationOnHealth = "transformationOnHealth";
    public static final String transformationOnThreat = "transformationOnThreat";
    public static final String transformationOnProximity = "transformationOnProximity";
    public static final String transformationOnAlone = "transformationOnAlone";
    public static final String transformationMimic = "transformationMimic";
    public static final String lastStand = "lastStand";

    public static final String abilities = "abilites";
}
