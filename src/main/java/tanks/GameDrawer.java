package tanks;

import basewindow.IDrawer;
import tanks.extension.Extension;

public class <PERSON><PERSON>rawer implements <PERSON>rawer
{
	@Override
	public void draw()
	{
		try
		{
			if (Game.enableExtensions)
			{
				for (int i = 0; i < Game.extensionRegistry.extensions.size(); i++)
				{
					Extension e = Game.extensionRegistry.extensions.get(i);

					e.preDraw();
				}
			}

			Panel.panel.draw();

			if (Game.enableExtensions)
			{
				for (int i = 0; i < Game.extensionRegistry.extensions.size(); i++)
				{
					Extension e = Game.extensionRegistry.extensions.get(i);

					e.draw();
				}
			}
		}
		catch (Throwable e)
		{
			if (e instanceof GameCrashedException)
				Game.displayCrashScreen(((GameCrashedException) e).originalException);
			else
				Game.displayCrashScreen(e);
		}
	}
}
