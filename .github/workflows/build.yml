name: Game
env:
  itchio_project: aehmttw/Tanks
  itchio_channel: Universal_JAR
on:
  workflow_dispatch:
  push:
    tags:
      - v*
jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          lfs: true
      - name: J<PERSON><PERSON><PERSON>
        uses: actions/setup-java@v3
        with:
          java-version: 8.0.312
          distribution: liberica
      - name: Setup Gradle
        uses: gradle/gradle-build-action@v2
      - name: Build
        run: ./gradlew build
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: artifact
          path: build/libs/*.jar
#  itch:
#    name: Itch
#    runs-on: ubuntu-latest
#    needs: build
#    steps:
#      - name: Checkout
#        uses: actions/checkout@v3
#        with:
#          lfs: true
#      - name: Download
#        uses: actions/download-artifact@v4.1.7
#        with:
#          name: artifact
#      - name: Version
#        run: |
#          echo "version=${GITHUB_REF/refs\/tags\/v/}" >> $GITHUB_ENV
#      - name: Upload
#        uses: robpc/itchio-upload-action@v1
#        with:
#          path: Tanks.jar
#          project: ${{ env.itchio_project }}
#          channel: ${{ env.itchio_channel }}
#          version: ${{ env.version }}
#          api-key: ${{ secrets.ITCHIO_API_KEY }}
