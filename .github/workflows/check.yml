name: Check

on:
  workflow_dispatch:
  pull_request:
  push:

jobs:
  codeql:
    name: Code<PERSON>
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          lfs: true

      - name: <PERSON><PERSON> Gradle
        uses: actions/cache@v2
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: JDK8
        uses: actions/setup-java@v3
        with:
          java-version: 8.0.312
          distribution: liberica

      - name: Setup Gradle
        uses: gradle/gradle-build-action@v2

      - name: Build
        run: ./gradlew build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: artifact
          path: build/libs/*.jar

#  codacy:
#    name: Codacy
#    runs-on: ubuntu-latest
#    steps:
#      - name: Checkout
#        uses: actions/checkout@main
#      - name: Analyze
#        uses: codacy/codacy-analysis-cli-action@master
