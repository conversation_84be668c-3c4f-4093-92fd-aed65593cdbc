# This file was generated by the Gradle 'init' task.
# https://docs.gradle.org/current/userguide/platforms.html#sub::toml-dependencies-format

[versions]
com-code-disaster-steamworks4j-steamworks4j = "1.10.0-SNAPSHOT"
com-code-disaster-steamworks4j-steamworks4j-lwjgl3 = "1.10.0-SNAPSHOT"
io-netty-netty-all = "4.1.68.Final"
org-apache-commons-commons-io = "1.3.2"
org-l33tlabs-twl-pngdecoder = "1.0"
org-lwjgl-lwjgl = "3.3.2"
org-lwjgl-lwjgl-assimp = "3.3.2"
org-lwjgl-lwjgl-glfw = "3.3.2"
org-lwjgl-lwjgl-openal = "3.3.2"
org-lwjgl-lwjgl-opengl = "3.3.2"
org-lwjgl-lwjgl-stb = "3.3.2"

[libraries]
com-code-disaster-steamworks4j-steamworks4j = { module = "com.code-disaster.steamworks4j:steamworks4j", version.ref = "com-code-disaster-steamworks4j-steamworks4j" }
com-code-disaster-steamworks4j-steamworks4j-lwjgl3 = { module = "com.code-disaster.steamworks4j:steamworks4j-lwjgl3", version.ref = "com-code-disaster-steamworks4j-steamworks4j-lwjgl3" }
io-netty-netty-all = { module = "io.netty:netty-all", version.ref = "io-netty-netty-all" }
org-apache-commons-commons-io = { module = "org.apache.commons:commons-io", version.ref = "org-apache-commons-commons-io" }
org-l33tlabs-twl-pngdecoder = { module = "org.l33tlabs.twl:pngdecoder", version.ref = "org-l33tlabs-twl-pngdecoder" }
org-lwjgl-lwjgl = { module = "org.lwjgl:lwjgl", version.ref = "org-lwjgl-lwjgl" }
org-lwjgl-lwjgl-assimp = { module = "org.lwjgl:lwjgl-assimp", version.ref = "org-lwjgl-lwjgl-assimp" }
org-lwjgl-lwjgl-glfw = { module = "org.lwjgl:lwjgl-glfw", version.ref = "org-lwjgl-lwjgl-glfw" }
org-lwjgl-lwjgl-openal = { module = "org.lwjgl:lwjgl-openal", version.ref = "org-lwjgl-lwjgl-openal" }
org-lwjgl-lwjgl-opengl = { module = "org.lwjgl:lwjgl-opengl", version.ref = "org-lwjgl-lwjgl-opengl" }
org-lwjgl-lwjgl-stb = { module = "org.lwjgl:lwjgl-stb", version.ref = "org-lwjgl-lwjgl-stb" }
